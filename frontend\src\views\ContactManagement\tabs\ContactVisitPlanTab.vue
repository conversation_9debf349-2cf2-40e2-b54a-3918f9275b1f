<template>
  <div class="visit-plan-tab">
    <!-- 统计卡片 -->
    <div class="statistics-row">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总计划数" :value="statistics.total" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已完成" :value="statistics.completed" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="完成率" :value="statistics.completionRate" suffix="%" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="准时率" :value="statistics.onTimeRate" suffix="%" />
        </el-col>
      </el-row>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        新建拜访计划
      </el-button>
      <div class="toolbar-right">
        <el-radio-group v-model="filterStatus" @change="loadVisitPlans">
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button label="planned">计划中</el-radio-button>
          <el-radio-button label="ongoing">进行中</el-radio-button>
          <el-radio-button label="completed">已完成</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 拜访计划列表 -->
    <el-table
      v-loading="loading"
      :data="visitPlanList"
      style="width: 100%"
      @row-click="handleRowClick"
    >
      <el-table-column prop="visitPlanName" label="计划名称" min-width="200" />
      <el-table-column prop="visitTime" label="拜访时间" width="160">
        <template #default="{ row }">
          {{ formatDateTime(row.visitTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="customerName" label="客户" width="150" />
      <el-table-column prop="contactName" label="联系人" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="VISIT_STATUS_MAP[row.status as VisitPlanStatus]?.color || 'info'">
            {{ VISIT_STATUS_MAP[row.status as VisitPlanStatus]?.label || row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button-group>
            <el-button v-if="row.status === 'planned'" size="small" @click.stop="handleEdit(row)">
              编辑
            </el-button>
            <el-button v-if="row.status === 'planned'" size="small" @click.stop="handlePostpone(row)">
              延期
            </el-button>
            <el-button v-if="['planned', 'ongoing'].includes(row.status)" size="small" @click.stop="handleCancel(row)">
              取消
            </el-button>
            <el-button v-if="row.status === 'ongoing'" size="small" type="success" @click.stop="handleComplete(row)">
              完成
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新建/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingPlan ? '编辑拜访计划' : '新建拜访计划'"
      width="600px"
      @close="resetForm"
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="计划名称" prop="visitPlanName">
          <el-input v-model="formData.visitPlanName" placeholder="请输入拜访计划名称" />
        </el-form-item>
        <el-form-item label="拜访时间" prop="visitTime">
          <el-date-picker
            v-model="formData.visitTime"
            type="datetime"
            placeholder="选择拜访时间"
            :disabled-date="disabledDate"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="拜访目的" prop="visitPurpose">
          <el-input
            v-model="formData.visitPurpose"
            type="textarea"
            :rows="3"
            placeholder="请输入拜访目的"
          />
        </el-form-item>
        <el-form-item label="提醒时间" prop="remindTime">
          <el-select v-model="formData.remindTime" placeholder="请选择提醒时间" style="width: 100%">
            <el-option
              v-for="item in REMIND_TIME_OPTIONS"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>

    <!-- 延期对话框 -->
    <el-dialog
      v-model="showPostponeDialog"
      title="延期拜访计划"
      width="500px"
    >
      <el-form ref="postponeFormRef" :model="postponeForm" :rules="postponeRules" label-width="100px">
        <el-form-item label="延期原因" prop="reason">
          <el-input v-model="postponeForm.reason" placeholder="请输入延期原因" />
        </el-form-item>
        <el-form-item label="新拜访时间" prop="newVisitTime">
          <el-date-picker
            v-model="postponeForm.newVisitTime"
            type="datetime"
            placeholder="选择新的拜访时间"
            :disabled-date="disabledDate"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="postponeForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入延期备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showPostponeDialog = false">取消</el-button>
        <el-button type="primary" @click="handlePostponeSubmit" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>

    <!-- 取消对话框 -->
    <el-dialog
      v-model="showCancelDialog"
      title="取消拜访计划"
      width="500px"
    >
      <el-form ref="cancelFormRef" :model="cancelForm" :rules="cancelRules" label-width="100px">
        <el-form-item label="取消原因" prop="reason">
          <el-input v-model="cancelForm.reason" placeholder="请输入取消原因" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="cancelForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入取消备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCancelDialog = false">取消</el-button>
        <el-button type="danger" @click="handleCancelSubmit" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>

    <!-- 完成对话框 -->
    <el-dialog
      v-model="showCompleteDialog"
      title="完成拜访计划"
      width="500px"
    >
      <el-form ref="completeFormRef" :model="completeForm" :rules="completeRules" label-width="100px">
        <el-form-item label="拜访记录" prop="followupContent">
          <el-input
            v-model="completeForm.followupContent"
            type="textarea"
            :rows="5"
            placeholder="请输入拜访记录，包括拜访结果、客户反馈等..."
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCompleteDialog = false">取消</el-button>
        <el-button type="success" @click="handleCompleteSubmit" :loading="submitting">完成</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import type { VisitPlan, VisitPlanStatistics, VisitPlanStatus } from '@/types/visit-plan';
import { VISIT_STATUS_MAP, REMIND_TIME_OPTIONS, DEFAULT_VISIT_PLAN } from '@/types/visit-plan';
import * as visitPlanApi from '@/api/visit-plan';
import { formatTime } from '@/utils/date';
import type { ContactEntity } from '../types';

defineOptions({
  name: 'ContactVisitPlanTab'
});

interface Props {
  entityData: ContactEntity;
}

const emit = defineEmits<{
  (e: 'update:entity', value: Record<string, any>): void;
  (e: 'update-count', count: number): void;
}>();

const props = defineProps<Props>();

// 状态
const loading = ref(false);
const submitting = ref(false);
const visitPlanList = ref<VisitPlan[]>([]);
const statistics = ref<VisitPlanStatistics>({
  total: 0,
  planned: 0,
  completed: 0,
  postponed: 0,
  cancelled: 0,
  completionRate: 0,
  onTimeRate: 0
});
const filterStatus = ref('');

// 对话框状态
const showAddDialog = ref(false);
const showPostponeDialog = ref(false);
const showCancelDialog = ref(false);
const showCompleteDialog = ref(false);

// 表单引用
const formRef = ref<FormInstance>();
const postponeFormRef = ref<FormInstance>();
const cancelFormRef = ref<FormInstance>();
const completeFormRef = ref<FormInstance>();

// 编辑状态
const editingPlan = ref<VisitPlan | null>(null);
const currentPlan = ref<VisitPlan | null>(null);

// 表单数据
const formData = reactive({
  visitPlanName: '',
  visitTime: '',
  visitPurpose: '',
  remindTime: 30,
  remark: ''
});

const postponeForm = reactive({
  reason: '',
  remark: '',
  newVisitTime: ''
});

const cancelForm = reactive({
  reason: '',
  remark: ''
});

const completeForm = reactive({
  followupContent: ''
});

// 表单验证规则
const formRules = {
  visitPlanName: [
    { required: true, message: '请输入计划名称', trigger: 'blur' }
  ],
  visitTime: [
    { required: true, message: '请选择拜访时间', trigger: 'change' }
  ],
  visitPurpose: [
    { required: true, message: '请输入拜访目的', trigger: 'blur' }
  ],
  remindTime: [
    { required: true, message: '请选择提醒时间', trigger: 'change' }
  ]
};

const postponeRules = {
  reason: [
    { required: true, message: '请输入延期原因', trigger: 'blur' }
  ],
  newVisitTime: [
    { required: true, message: '请选择新的拜访时间', trigger: 'change' }
  ]
};

const cancelRules = {
  reason: [
    { required: true, message: '请输入取消原因', trigger: 'blur' }
  ]
};

const completeRules = {
  followupContent: [
    { required: true, message: '请输入拜访记录', trigger: 'blur' }
  ]
};

// 禁用过去的日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
};

// 格式化日期时间
const formatDateTime = (dateStr: string | Date | undefined) => {
  if (!dateStr) return '-';
  return formatTime(dateStr);
};

// 加载拜访计划列表
const loadVisitPlans = async () => {
  if (!props.entityData.id) return;
  
  loading.value = true;
  try {
    const res = await visitPlanApi.getVisitPlanByObject('contact', props.entityData.id);
    let plans = res.data || [];
    
    // 过滤状态
    if (filterStatus.value) {
      plans = plans.filter((item: VisitPlan) => item.status === filterStatus.value);
    }
    
    visitPlanList.value = plans;
    
    // 通知父组件更新计数
    emit('update-count', plans.length);
  } catch (error) {
    console.error('加载拜访计划失败:', error);
    ElMessage.error('加载拜访计划失败');
    visitPlanList.value = [];
  } finally {
    loading.value = false;
  }
};

// 加载统计信息
const loadStatistics = async () => {
  try {
    const res = await visitPlanApi.getVisitPlanStatistics();
    statistics.value = res.data;
  } catch (error) {
    console.error('加载统计信息失败:', error);
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, DEFAULT_VISIT_PLAN);
  editingPlan.value = null;
  formRef.value?.clearValidate();
};

// 处理行点击
const handleRowClick = (row: VisitPlan) => {
  // 可以在这里添加行点击处理逻辑
};

// 处理编辑
const handleEdit = (row: VisitPlan) => {
  editingPlan.value = row;
  Object.assign(formData, {
    visitPlanName: row.visitPlanName,
    visitTime: row.visitTime,
    visitPurpose: row.visitPurpose,
    remindTime: row.remindTime,
    remark: row.remark
  });
  showAddDialog.value = true;
};

// 处理延期
const handlePostpone = (row: VisitPlan) => {
  currentPlan.value = row;
  Object.assign(postponeForm, {
    reason: '',
    remark: '',
    newVisitTime: ''
  });
  showPostponeDialog.value = true;
};

// 处理取消
const handleCancel = (row: VisitPlan) => {
  currentPlan.value = row;
  Object.assign(cancelForm, {
    reason: '',
    remark: ''
  });
  showCancelDialog.value = true;
};

// 处理完成
const handleComplete = (row: VisitPlan) => {
  currentPlan.value = row;
  Object.assign(completeForm, {
    followupContent: ''
  });
  showCompleteDialog.value = true;
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;
  
  if (!props.entityData.customerId) {
    ElMessage.error('缺少客户信息，无法创建拜访计划');
    return;
  }
  
  try {
    submitting.value = true;
    const data = {
      ...formData,
      contactId: props.entityData.id,
      customerId: props.entityData.customerId
    };
    
    if (editingPlan.value) {
      await visitPlanApi.updateVisitPlan({ ...editingPlan.value, ...data });
      ElMessage.success('修改成功');
    } else {
      await visitPlanApi.addVisitPlan(data);
      ElMessage.success('创建成功');
    }
    
    showAddDialog.value = false;
    loadVisitPlans();
    loadStatistics();
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  } finally {
    submitting.value = false;
  }
};

// 延期提交
const handlePostponeSubmit = async () => {
  if (!postponeFormRef.value || !currentPlan.value) return;
  
  const valid = await postponeFormRef.value.validate().catch(() => false);
  if (!valid) return;
  
  try {
    submitting.value = true;
    await visitPlanApi.postponeVisitPlan(currentPlan.value.id!, {
      reason: postponeForm.reason,
      remark: postponeForm.remark,
      newVisitTime: formatTime(postponeForm.newVisitTime)
    });
    
    ElMessage.success('延期成功');
    showPostponeDialog.value = false;
    loadVisitPlans();
    loadStatistics();
  } catch (error) {
    console.error('延期失败:', error);
    ElMessage.error('延期失败');
  } finally {
    submitting.value = false;
  }
};

// 取消提交
const handleCancelSubmit = async () => {
  if (!cancelFormRef.value || !currentPlan.value) return;
  
  const valid = await cancelFormRef.value.validate().catch(() => false);
  if (!valid) return;
  
  try {
    submitting.value = true;
    await visitPlanApi.cancelVisitPlan(currentPlan.value.id!, {
      reason: cancelForm.reason,
      remark: cancelForm.remark
    });
    
    ElMessage.success('取消成功');
    showCancelDialog.value = false;
    loadVisitPlans();
    loadStatistics();
  } catch (error) {
    console.error('取消失败:', error);
    ElMessage.error('取消失败');
  } finally {
    submitting.value = false;
  }
};

// 完成提交
const handleCompleteSubmit = async () => {
  if (!completeFormRef.value || !currentPlan.value) return;
  
  const valid = await completeFormRef.value.validate().catch(() => false);
  if (!valid) return;
  
  try {
    submitting.value = true;
    await visitPlanApi.completeVisitPlan(currentPlan.value.id!, {
      followupContent: completeForm.followupContent
    });
    
    ElMessage.success('完成成功');
    showCompleteDialog.value = false;
    loadVisitPlans();
    loadStatistics();
  } catch (error) {
    console.error('完成失败:', error);
    ElMessage.error('完成失败');
  } finally {
    submitting.value = false;
  }
};

// 监听联系人ID变化
const watchEntityId = () => {
  if (props.entityData.id) {
    loadVisitPlans();
    loadStatistics();
  }
};

onMounted(() => {
  watchEntityId();
});
</script>

<style scoped>
.visit-plan-tab {
  padding: 20px;
}

.statistics-row {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}

:deep(.el-button-group .el-button) {
  margin-left: 0;
}

:deep(.el-statistic__number) {
  font-weight: bold;
  color: var(--el-color-primary);
}

@media (max-width: 768px) {
  .visit-plan-tab {
    padding: 12px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-right {
    justify-content: center;
  }
}
</style>
