<template>
    <el-dialog
        :title="'转化线索 - ' + leadName"
        v-model="dialogVisible"
        width="700px"
        @close="handleClose"
        class="convert-lead-dialog"
    >
        <el-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-width="120px"
            class="convert-form"
        >
            <!-- 转化类型选择 -->
            <el-form-item label="转化类型" prop="convertType">
                <el-radio-group v-model="formData.convertType" class="convert-type-group">
                    <el-radio-button label="existing">转化为已有客户</el-radio-button>
                    <el-radio-button label="new">转化为新客户</el-radio-button>
                </el-radio-group>
            </el-form-item>

            <!-- 已有客户选择 -->
            <template v-if="formData.convertType === 'existing'">
                <el-form-item label="选择客户" prop="customerId">
                    <el-select
                        v-model="formData.customerId"
                        filterable
                        remote
                        :remote-method="handleCustomerSearch"
                        :loading="customerLoading"
                        placeholder="请输入客户名称搜索"
                        class="customer-select"
                        clearable
                        @focus="loadFrequentCustomers"
                        default-first-option
                    >
                        <el-option
                            v-for="item in customerOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                            <div class="customer-option">
                                <span class="customer-name">{{ item.label }}</span>
                                <span class="customer-industry">{{ item.industry }}</span>
                            </div>
                        </el-option>
                    </el-select>
                </el-form-item>
            </template>

            <!-- 新客户信息 -->
            <template v-if="formData.convertType === 'new'">
                <el-form-item label="客户名称" prop="customerName">
                    <el-input 
                        v-model="formData.customerName" 
                        placeholder="请输入客户名称"
                        class="customer-input"
                    />
                </el-form-item>
                <el-form-item label="客户行业" prop="industry">
                    <el-select 
                        v-model="formData.industry" 
                        placeholder="请选择行业"
                        class="industry-select"
                        clearable
                    >
                        <el-option
                            v-for="item in industryOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </template>

            <el-divider content-position="left">联系人信息</el-divider>

            <!-- 联系人信息 -->
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="联系人姓名" prop="contactName">
                        <el-input 
                            v-model="formData.contactName" 
                            placeholder="请输入联系人姓名"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="联系人职位" prop="position">
                        <el-input 
                            v-model="formData.position" 
                            placeholder="请输入联系人职位"
                        />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="联系电话" prop="phone">
                        <el-input 
                            v-model="formData.phone" 
                            placeholder="请输入联系电话"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="电子邮箱" prop="email">
                        <el-input 
                            v-model="formData.email" 
                            placeholder="请输入电子邮箱"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="loading">
                    确 定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { searchCustomers } from '@/api/crm/customer/index';
import { convertLead } from '@/api/crm/leads/index';
import type { LeadConvertData } from '@/api/crm/leads/types';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';
import { computed, reactive, ref, watch } from 'vue';

const props = defineProps({
    modelValue: Boolean,
    leadId: {
        type: Number,
        required: true
    },
    leadName: {
        type: String,
        required: true
    }
});

const emit = defineEmits(['update:modelValue', 'success']);

const dialogVisible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
});

const formRef = ref<FormInstance>();
const loading = ref(false);
const customerLoading = ref(false);
const customerOptions = ref<Array<{ label: string; value: number; industry: string }>>([]);

// 行业选项
const industryOptions = [
    { label: '互联网', value: 'INTERNET' },
    { label: '金融', value: 'FINANCE' },
    { label: '教育', value: 'EDUCATION' },
    { label: '医疗', value: 'MEDICAL' },
    { label: '制造业', value: 'MANUFACTURING' },
    { label: '零售', value: 'RETAIL' },
    { label: '其他', value: 'OTHER' }
];

const formData = reactive<{
    convertType: 'existing' | 'new';
    customerId?: number;
    customerName: string;
    industry: string;
    contactName: string;
    position: string;
    phone: string;
    email: string;
}>({
    convertType: 'existing',
    customerId: undefined,
    customerName: '',
    industry: '',
    contactName: '',
    position: '',
    phone: '',
    email: ''
});

const rules = {
    convertType: [{ required: true, message: '请选择转化类型' }],
    customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
    customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
    industry: [{ required: true, message: '请选择行业', trigger: 'change' }],
    contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
    phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
    email: [{ required: true, message: '请输入电子邮箱', trigger: 'blur' }]
};

// 搜索客户
// 加载最近常用的客户
const loadFrequentCustomers = async () => {
    customerLoading.value = true;
    try {
        const res = await searchCustomers({ recent: true, limit: 10 });
        customerOptions.value = res.data.map(item => ({
            label: item.customerName,
            value: item.id,
            industry: item.industry
        }));
    } catch (error) {
        console.error('加载常用客户失败:', error);
    } finally {
        customerLoading.value = false;
    }
};

const handleCustomerSearch = async (query: string) => {
    if (query) {
        customerLoading.value = true;
        try {
            const res = await searchCustomers({ keyword: query });
            customerOptions.value = res.data.map(item => ({
                label: item.customerName,
                value: item.id,
                industry: item.industry
            }));
        } catch (error) {
            console.error('搜索客户失败:', error);
        } finally {
            customerLoading.value = false;
        }
    }
};

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return;
    
    await formRef.value.validate(async (valid) => {
        if (valid) {
            loading.value = true;
            try {
                const data: LeadConvertData = {
                    leadId: props.leadId,
                    convertType: formData.convertType,
                    customerId: formData.customerId,
                    customerName: formData.customerName,
                    industry: formData.industry,
                    contact: {
                        name: formData.contactName,
                        position: formData.position,
                        phone: formData.phone,
                        email: formData.email
                    }
                };

                const res = await convertLead(data);
                if (res.data.code === 200) {
                    ElMessage.success('转化成功');
                    emit('success');
                    handleClose();
                } else {
                    ElMessage.error(res.data.msg || '转化失败');
                }
            } catch (error) {
                console.error('转化失败:', error);
                ElMessage.error('转化失败，请稍后重试');
            } finally {
                loading.value = false;
            }
        }
    });
};

const handleClose = () => {
    dialogVisible.value = false;
    formRef.value?.resetFields();
};

// 监听转化类型变化，重置表单
watch(() => formData.convertType, () => {
    formData.customerId = undefined;
    formData.customerName = '';
    formData.industry = '';
});
</script>

<style scoped>
.convert-lead-dialog {
    :deep(.el-dialog__body) {
        padding: 20px 30px;
    }
}

.convert-form {
    .convert-type-group {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
    }

    .customer-select {
        width: 100%;
    }

    .customer-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .customer-name {
            font-weight: 500;
        }
        
        .customer-industry {
            color: var(--el-text-color-secondary);
            font-size: 12px;
        }
    }

    .customer-input,
    .industry-select {
        width: 100%;
    }

    .el-divider {
        margin: 20px 0;
    }
}

.dialog-footer {
    text-align: right;
    padding-top: 20px;
}
</style>
