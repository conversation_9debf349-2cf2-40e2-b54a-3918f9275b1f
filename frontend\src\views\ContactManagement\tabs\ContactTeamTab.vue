<template>
  <div class="contact-team-tab">
    <!-- 团队分配状态 -->
    <div class="team-status-section">
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <span>团队分配状态</span>
            <el-button
              v-if="!readonly"
              type="primary"
              size="small"
              @click="openAssignDialog"
            >
              {{ currentTeam ? '重新分配' : '分配团队' }}
            </el-button>
          </div>
        </template>

        <div v-if="currentTeam" class="current-team">
          <div class="team-info">
            <el-avatar :size="40" :src="currentTeam.avatar">
              {{ currentTeam.teamName?.charAt(0) }}
            </el-avatar>
            <div class="team-details">
              <h4>{{ currentTeam.teamName }}</h4>
              <p class="team-leader">负责人：{{ currentTeam.leaderName || '未设置' }}</p>
              <p class="assign-time">分配时间：{{ formatDate(currentTeam.createTime) }}</p>
            </div>
          </div>
          <div class="team-actions">
            <el-button
              v-if="!readonly"
              type="danger"
              size="small"
              plain
              @click="handleUnassign"
            >
              取消分配
            </el-button>
          </div>
        </div>

        <div v-else class="no-team">
          <el-empty description="暂未分配团队">
            <el-button
              v-if="!readonly"
              type="primary"
              @click="openAssignDialog"
            >
              分配团队
            </el-button>
          </el-empty>
        </div>
      </el-card>
    </div>

    <!-- 团队成员列表 -->
    <div v-if="currentTeam" class="team-members-section">
      <el-card class="members-card">
        <template #header>
          <div class="card-header">
            <span>团队成员 ({{ teamMembers.length }}人)</span>
            <div class="header-actions">
              <!-- 团队管理按钮 -->
              <div v-if="canManageTeam && !readonly" class="team-manage-actions">
                <el-button
                  type="primary"
                  size="small"
                  icon="Plus"
                  @click="openAddMemberDialog"
                >
                  添加成员
                </el-button>
              </div>
              <!-- 视图切换 -->
              <el-radio-group v-model="viewMode" size="small">
                <el-radio-button label="list">列表</el-radio-button>
                <el-radio-button label="grid">网格</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="members-list">
          <el-table :data="teamMembers" style="width: 100%" v-loading="loading">
            <el-table-column width="60">
              <template #default="{ row }">
                <el-avatar :size="40" :src="row.avatar">
                  {{ row.nickName?.charAt(0) }}
                </el-avatar>
              </template>
            </el-table-column>
            <el-table-column prop="nickName" label="姓名" width="120">
              <template #default="{ row }">
                <div class="member-name">
                  <span>{{ row.nickName || row.userName }}</span>
                  <el-tag
                    v-if="row.roleType === 'owner'"
                    type="danger"
                    size="small"
                    style="margin-left: 8px;"
                  >
                    负责人
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="userName" label="用户名" width="120" />
            <el-table-column prop="roleType" label="角色" width="100">
              <template #default="{ row }">
                <el-tag :type="getRoleTagType(row.roleType)" size="small">
                  {{ getRoleLabel(row.roleType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="joinTime" label="加入时间" width="150">
              <template #default="{ row }">
                {{ formatDate(row.joinTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === '0' ? 'success' : 'danger'" size="small">
                  {{ row.status === '0' ? '正常' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="160" fixed="right">
              <template #default="{ row }">
                <el-button link size="small" @click="viewMemberDetail(row)">
                  详情
                </el-button>
                <el-button
                  v-if="canManageTeam && !readonly && row.roleType !== 'owner'"
                  link
                  size="small"
                  type="danger"
                  @click="handleRemoveMember(row)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 网格视图 -->
        <div v-else class="members-grid">
          <el-row :gutter="16">
            <el-col
              :xs="24" :sm="12" :md="8" :lg="6"
              v-for="member in teamMembers"
              :key="member.id"
            >
              <div class="member-card" @click="viewMemberDetail(member)">
                <div class="member-avatar">
                  <el-avatar :size="60" :src="member.avatar">
                    {{ member.nickName?.charAt(0) }}
                  </el-avatar>
                  <div class="status-indicator" :class="member.status === '0' ? 'online' : 'offline'"></div>
                </div>
                <div class="member-info">
                  <h5>{{ member.nickName || member.userName }}</h5>
                  <p class="username">@{{ member.userName }}</p>
                  <el-tag :type="getRoleTagType(member.roleType)" size="small">
                    {{ getRoleLabel(member.roleType) }}
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 空状态 -->
        <div v-if="teamMembers.length === 0 && !loading" class="empty-members">
          <el-empty description="团队暂无成员" />
        </div>
      </el-card>
    </div>

    <!-- 团队分配对话框 -->
    <TeamAssignDialog
      v-model:visible="assignDialogVisible"
      :biz-id="entityData?.id"
      biz-type="CONTACT"
      :biz-name="entityData?.name"
      title="分配联系人团队"
      @success="handleAssignSuccess"
    />

    <!-- 添加成员对话框 -->
    <el-dialog
      v-model="addMemberDialogVisible"
      title="添加团队成员"
      width="600px"
      @close="selectedUsers = []; selectedRole = 'member'"
    >
      <el-form label-width="80px">
        <el-form-item label="选择用户">
          <el-select
            v-model="selectedUsers"
            multiple
            placeholder="请选择要添加的用户"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="user in availableUsers"
              :key="user.userId"
              :label="user.nickName || user.userName"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="角色">
          <el-radio-group v-model="selectedRole">
            <el-radio label="member">普通成员</el-radio>
            <el-radio label="admin">管理员</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addMemberDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAddMembers">确定</el-button>
      </template>
    </el-dialog>

    <!-- 成员详情抽屉 -->
    <el-drawer v-model="memberDetailVisible" title="成员详情" size="400px">
      <div v-if="selectedMember" class="member-detail">
        <div class="detail-header">
          <el-avatar :size="80" :src="selectedMember.avatar">
            {{ selectedMember.nickName?.charAt(0) }}
          </el-avatar>
          <h3>{{ selectedMember.nickName || selectedMember.userName }}</h3>
          <p>@{{ selectedMember.userName }}</p>
        </div>
        <div class="detail-info">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="角色">
              <el-tag :type="getRoleTagType(selectedMember.roleType)">
                {{ getRoleLabel(selectedMember.roleType) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="selectedMember.status === '0' ? 'success' : 'danger'">
                {{ selectedMember.status === '0' ? '正常' : '停用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="加入时间">
              {{ formatDate(selectedMember.joinTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="所属团队">
              {{ selectedMember.teamName || currentTeam?.teamName }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import {
  batchAddTeamMembers,
  getAvailableUsers,
  getTeamByBiz,
  getTeamMembersByBiz,
  removeTeamMember,
  unassignTeamFromBiz
} from '@/api/team-relation'
import TeamAssignDialog from '@/components/TeamAssignDialog.vue'
import { useUserStore } from '@/store/modules/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, ref, watch } from 'vue'

// 定义接口
interface TeamInfo {
  teamId: number
  teamName: string
  leaderName?: string
  createTime: string
  avatar?: string
}

interface TeamMember {
  id: number
  teamId: number
  userId: number
  userName: string
  nickName: string
  roleType: string
  joinTime: string
  status: string
  teamName?: string
  avatar?: string
}

interface Props {
  entityData?: any
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// 响应式数据
const loading = ref(false)
const viewMode = ref<'list' | 'grid'>('list')
const currentTeam = ref<TeamInfo | null>(null)
const teamMembers = ref<TeamMember[]>([])
const assignDialogVisible = ref(false)
const memberDetailVisible = ref(false)
const selectedMember = ref<TeamMember | null>(null)
const addMemberDialogVisible = ref(false)
const availableUsers = ref<any[]>([])
const selectedUsers = ref<number[]>([])
const selectedRole = ref<'member' | 'admin' | 'owner'>('member')

// 用户store
const userStore = useUserStore()

// 权限检查
const canManageTeam = ref(false)

// 检查团队管理权限
const checkTeamManagePermission = async () => {
  if (!props.entityData?.id) {
    canManageTeam.value = false
    return
  }

  try {
    // 超级管理员拥有所有权限
    if (userStore.roles.includes('admin')) {
      canManageTeam.value = true
      return
    }

    // 团队管理员权限
    if (userStore.roles.includes('team_admin')) {
      canManageTeam.value = true
      return
    }

    // 检查是否是团队负责人或管理员
    // 在新架构中，通过团队成员信息来判断权限
    if (currentTeam.value && teamMembers.value.length > 0) {
      const currentUserId = userStore.userId
      const currentUserMember = teamMembers.value.find(member => member.userId === currentUserId)
      canManageTeam.value = !!(currentUserMember && ['owner', 'admin'].includes(currentUserMember.roleType))
    } else {
      canManageTeam.value = false
    }
  } catch (error) {
    console.error('检查权限失败:', error)
    canManageTeam.value = false
  }
}

// 方法
const loadTeamInfo = async () => {
  if (!props.entityData?.id) return

  loading.value = true
  try {
    const response = await getTeamByBiz(props.entityData.id, 'CONTACT')
    currentTeam.value = response.data || null
  } catch (error) {
    console.error('加载团队信息失败:', error)
  } finally {
    loading.value = false
  }
}

const loadTeamMembers = async () => {
  if (!props.entityData?.id) return

  loading.value = true
  try {
    const response = await getTeamMembersByBiz(props.entityData.id, 'CONTACT')
    teamMembers.value = response.data?.rows || response.data || []
  } catch (error) {
    console.error('加载团队成员失败:', error)
    teamMembers.value = []
  } finally {
    loading.value = false
  }
}

const openAssignDialog = () => {
  assignDialogVisible.value = true
}

const handleUnassign = async () => {
  if (!props.entityData?.id) return

  try {
    await ElMessageBox.confirm(
      '确定要取消当前的团队分配吗？',
      '确认取消分配',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await unassignTeamFromBiz(props.entityData.id, 'CONTACT')
    ElMessage.success('取消分配成功')

    // 重新加载数据
    currentTeam.value = null
    teamMembers.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消分配失败:', error)
      ElMessage.error('取消分配失败')
    }
  }
}

const handleAssignSuccess = () => {
  ElMessage.success('团队分配成功')
  loadTeamInfo()
  loadTeamMembers()
}

const viewMemberDetail = (member: TeamMember) => {
  selectedMember.value = member
  memberDetailVisible.value = true
}

// 打开添加成员对话框
const openAddMemberDialog = async () => {
  try {
    // 加载可选用户列表
    const response = await getAvailableUsers()
    availableUsers.value = response.data || []
    addMemberDialogVisible.value = true
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  }
}

// 移除团队成员
const handleRemoveMember = async (member: TeamMember) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除成员 "${member.nickName || member.userName}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 新API需要teamId和userId参数
    await removeTeamMember(member.teamId, member.userId)
    ElMessage.success('移除成员成功')

    // 重新加载团队成员列表
    loadTeamMembers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除成员失败:', error)
      ElMessage.error('移除成员失败')
    }
  }
}

// 添加团队成员
const handleAddMembers = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要添加的用户')
    return
  }

  if (!currentTeam.value?.teamId) {
    ElMessage.error('未找到团队信息')
    return
  }

  try {
    // 批量添加成员 - 使用新的API
    await batchAddTeamMembers(
      currentTeam.value.teamId,
      selectedUsers.value,
      selectedRole.value
    )

    ElMessage.success('添加成员成功')
    addMemberDialogVisible.value = false
    selectedUsers.value = []
    selectedRole.value = 'member'

    // 重新加载团队成员列表
    loadTeamMembers()
  } catch (error) {
    console.error('添加成员失败:', error)
    ElMessage.error('添加成员失败')
  }
}

// 辅助方法
const getRoleTagType = (roleType: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    owner: 'danger',
    admin: 'warning',
    member: 'info'
  }
  return typeMap[roleType] || 'info'
}

const getRoleLabel = (roleType: string) => {
  const labelMap: Record<string, string> = {
    owner: '负责人',
    admin: '管理员',
    member: '成员'
  }
  return labelMap[roleType] || '未知'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  loadTeamInfo()
  loadTeamMembers()
  checkTeamManagePermission()
})

// 监听实体数据变化
watch(() => props.entityData?.id, () => {
  if (props.entityData?.id) {
    loadTeamInfo()
    loadTeamMembers()
    checkTeamManagePermission()
  }
})
</script>

<style scoped>
.contact-team-tab {
  padding: 20px;
}

/* 团队状态卡片 */
.team-status-section {
  margin-bottom: 20px;
}

.status-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-team {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.team-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.team-details h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
}

.team-details p {
  margin: 2px 0;
  color: #909399;
  font-size: 14px;
}

.team-leader {
  color: #606266;
}

.assign-time {
  color: #909399;
  font-size: 12px;
}

.no-team {
  text-align: center;
  padding: 40px 0;
}

/* 团队成员卡片 */
.members-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 成员列表 */
.members-list {
  margin-top: 16px;
}

.member-name {
  display: flex;
  align-items: center;
}

/* 成员网格 */
.members-grid {
  margin-top: 16px;
}

.member-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
  text-align: center;
}

.member-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.member-avatar {
  position: relative;
  display: inline-block;
  margin-bottom: 12px;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #fff;
}

.status-indicator.online {
  background-color: #67c23a;
}

.status-indicator.offline {
  background-color: #f56c6c;
}

.member-info h5 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 14px;
}

.member-info .username {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 12px;
}

/* 空状态 */
.empty-members {
  text-align: center;
  padding: 40px 0;
}

/* 成员详情 */
.member-detail {
  padding: 20px;
}

.detail-header {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-header h3 {
  margin: 12px 0 4px 0;
  color: #303133;
}

.detail-header p {
  margin: 0;
  color: #909399;
}

.detail-info {
  margin-top: 20px;
}

/* 团队管理按钮样式 */
.team-manage-actions {
  margin-right: 12px;
}

.team-manage-actions .el-button {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contact-team-tab {
    padding: 12px;
  }

  .current-team {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .team-actions {
    align-self: stretch;
  }

  .members-card .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .team-manage-actions {
    margin-right: 0;
    margin-bottom: 8px;
  }
}
</style>
