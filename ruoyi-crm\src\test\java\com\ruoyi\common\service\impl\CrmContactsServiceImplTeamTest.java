package com.ruoyi.common.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.domain.entity.CrmTeamRelation;
import com.ruoyi.common.mapper.CrmContactsMapper;
import com.ruoyi.crm.service.ICrmTeamRelationService;

/**
 * CrmContactsServiceImpl 团队关联方法测试
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("联系人服务团队关联方法测试")
class CrmContactsServiceImplTeamTest {

    @Mock
    private CrmContactsMapper crmContactsMapper;

    @Mock
    private ICrmTeamRelationService crmTeamRelationService;

    @InjectMocks
    private CrmContactsServiceImpl crmContactsService;

    private CrmContacts testContact;
    private CrmTeamRelation testTeamRelation;

    @BeforeEach
    void setUp() {
        testContact = new CrmContacts();
        testContact.setId(1L);
        testContact.setName("测试联系人");
        testContact.setMobile("13800138000");

        testTeamRelation = new CrmTeamRelation();
        testTeamRelation.setId(1L);
        testTeamRelation.setTeamId(100L);
        testTeamRelation.setBizId(1L);
        testTeamRelation.setBizType(CrmTeamRelation.BizType.CONTACT);
        testTeamRelation.setTeamName("测试团队");
    }

    @Nested
    @DisplayName("查询联系人团队关联信息")
    class GetContactTeamRelationTests {

        @Test
        @DisplayName("正常查询团队关联信息")
        void testGetContactTeamRelation_Success() {
            // Given
            Long contactId = 1L;
            when(crmTeamRelationService.getTeamRelationByBiz(contactId, CrmTeamRelation.BizType.CONTACT))
                .thenReturn(testTeamRelation);

            // When
            CrmTeamRelation result = crmContactsService.getContactTeamRelation(contactId);

            // Then
            assertNotNull(result);
            assertEquals(testTeamRelation.getTeamId(), result.getTeamId());
            assertEquals(testTeamRelation.getBizId(), result.getBizId());
            assertEquals(CrmTeamRelation.BizType.CONTACT, result.getBizType());
            verify(crmTeamRelationService).getTeamRelationByBiz(contactId, CrmTeamRelation.BizType.CONTACT);
        }

        @Test
        @DisplayName("联系人ID为null时返回null")
        void testGetContactTeamRelation_NullContactId() {
            // When
            CrmTeamRelation result = crmContactsService.getContactTeamRelation(null);

            // Then
            assertNull(result);
            verify(crmTeamRelationService, never()).getTeamRelationByBiz(any(), any());
        }

        @Test
        @DisplayName("未找到团队关联时返回null")
        void testGetContactTeamRelation_NotFound() {
            // Given
            Long contactId = 1L;
            when(crmTeamRelationService.getTeamRelationByBiz(contactId, CrmTeamRelation.BizType.CONTACT))
                .thenReturn(null);

            // When
            CrmTeamRelation result = crmContactsService.getContactTeamRelation(contactId);

            // Then
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("分配联系人到团队")
    class AssignContactToTeamTests {

        @Test
        @DisplayName("正常分配联系人到团队")
        void testAssignContactToTeam_Success() {
            // Given
            Long contactId = 1L;
            Long teamId = 100L;
            when(crmContactsMapper.selectCrmContactsById(contactId)).thenReturn(testContact);
            when(crmTeamRelationService.assignTeamToBiz(teamId, contactId, CrmTeamRelation.BizType.CONTACT))
                .thenReturn(1);

            // When
            int result = crmContactsService.assignContactToTeam(contactId, teamId);

            // Then
            assertEquals(1, result);
            verify(crmContactsMapper).selectCrmContactsById(contactId);
            verify(crmTeamRelationService).assignTeamToBiz(teamId, contactId, CrmTeamRelation.BizType.CONTACT);
        }

        @Test
        @DisplayName("联系人ID为null时返回0")
        void testAssignContactToTeam_NullContactId() {
            // When
            int result = crmContactsService.assignContactToTeam(null, 100L);

            // Then
            assertEquals(0, result);
            verify(crmContactsMapper, never()).selectCrmContactsById(any());
            verify(crmTeamRelationService, never()).assignTeamToBiz(any(), any(), any());
        }

        @Test
        @DisplayName("团队ID为null时返回0")
        void testAssignContactToTeam_NullTeamId() {
            // When
            int result = crmContactsService.assignContactToTeam(1L, null);

            // Then
            assertEquals(0, result);
            verify(crmContactsMapper, never()).selectCrmContactsById(any());
            verify(crmTeamRelationService, never()).assignTeamToBiz(any(), any(), any());
        }

        @Test
        @DisplayName("联系人不存在时抛出异常")
        void testAssignContactToTeam_ContactNotFound() {
            // Given
            Long contactId = 1L;
            Long teamId = 100L;
            when(crmContactsMapper.selectCrmContactsById(contactId)).thenReturn(null);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> crmContactsService.assignContactToTeam(contactId, teamId));
            
            assertEquals("联系人不存在: " + contactId, exception.getMessage());
            verify(crmContactsMapper).selectCrmContactsById(contactId);
            verify(crmTeamRelationService, never()).assignTeamToBiz(any(), any(), any());
        }
    }

    @Nested
    @DisplayName("取消联系人团队分配")
    class UnassignContactFromTeamTests {

        @Test
        @DisplayName("正常取消团队分配")
        void testUnassignContactFromTeam_Success() {
            // Given
            Long contactId = 1L;
            when(crmTeamRelationService.unassignTeamFromBiz(contactId, CrmTeamRelation.BizType.CONTACT))
                .thenReturn(1);

            // When
            int result = crmContactsService.unassignContactFromTeam(contactId);

            // Then
            assertEquals(1, result);
            verify(crmTeamRelationService).unassignTeamFromBiz(contactId, CrmTeamRelation.BizType.CONTACT);
        }

        @Test
        @DisplayName("联系人ID为null时返回0")
        void testUnassignContactFromTeam_NullContactId() {
            // When
            int result = crmContactsService.unassignContactFromTeam(null);

            // Then
            assertEquals(0, result);
            verify(crmTeamRelationService, never()).unassignTeamFromBiz(any(), any());
        }
    }

    @Nested
    @DisplayName("批量分配联系人到团队")
    class BatchAssignContactsToTeamTests {

        @Test
        @DisplayName("正常批量分配联系人到团队")
        void testBatchAssignContactsToTeam_Success() {
            // Given
            List<Long> contactIds = Arrays.asList(1L, 2L, 3L);
            Long teamId = 100L;
            when(crmTeamRelationService.batchAssignTeamToBiz(teamId, contactIds, CrmTeamRelation.BizType.CONTACT))
                .thenReturn(3);

            // When
            int result = crmContactsService.batchAssignContactsToTeam(contactIds, teamId);

            // Then
            assertEquals(3, result);
            verify(crmTeamRelationService).batchAssignTeamToBiz(teamId, contactIds, CrmTeamRelation.BizType.CONTACT);
        }

        @Test
        @DisplayName("联系人ID列表为null时返回0")
        void testBatchAssignContactsToTeam_NullContactIds() {
            // When
            int result = crmContactsService.batchAssignContactsToTeam(null, 100L);

            // Then
            assertEquals(0, result);
            verify(crmTeamRelationService, never()).batchAssignTeamToBiz(any(), any(), any());
        }

        @Test
        @DisplayName("联系人ID列表为空时返回0")
        void testBatchAssignContactsToTeam_EmptyContactIds() {
            // When
            int result = crmContactsService.batchAssignContactsToTeam(Arrays.asList(), 100L);

            // Then
            assertEquals(0, result);
            verify(crmTeamRelationService, never()).batchAssignTeamToBiz(any(), any(), any());
        }

        @Test
        @DisplayName("团队ID为null时返回0")
        void testBatchAssignContactsToTeam_NullTeamId() {
            // When
            int result = crmContactsService.batchAssignContactsToTeam(Arrays.asList(1L, 2L), null);

            // Then
            assertEquals(0, result);
            verify(crmTeamRelationService, never()).batchAssignTeamToBiz(any(), any(), any());
        }
    }

    @Nested
    @DisplayName("查询联系人详情包含团队信息")
    class SelectCrmContactsWithTeamByIdTests {

        @Test
        @DisplayName("正常查询联系人详情")
        void testSelectCrmContactsWithTeamById_Success() {
            // Given
            Long contactId = 1L;
            when(crmContactsMapper.selectCrmContactsById(contactId)).thenReturn(testContact);

            // When
            CrmContacts result = crmContactsService.selectCrmContactsWithTeamById(contactId);

            // Then
            assertNotNull(result);
            assertEquals(testContact.getId(), result.getId());
            assertEquals(testContact.getName(), result.getName());
            verify(crmContactsMapper).selectCrmContactsById(contactId);
        }

        @Test
        @DisplayName("联系人ID为null时返回null")
        void testSelectCrmContactsWithTeamById_NullId() {
            // When
            CrmContacts result = crmContactsService.selectCrmContactsWithTeamById(null);

            // Then
            assertNull(result);
            verify(crmContactsMapper, never()).selectCrmContactsById(any());
        }

        @Test
        @DisplayName("联系人不存在时返回null")
        void testSelectCrmContactsWithTeamById_NotFound() {
            // Given
            Long contactId = 1L;
            when(crmContactsMapper.selectCrmContactsById(contactId)).thenReturn(null);

            // When
            CrmContacts result = crmContactsService.selectCrmContactsWithTeamById(contactId);

            // Then
            assertNull(result);
            verify(crmContactsMapper).selectCrmContactsById(contactId);
        }
    }
}
